# 修复版main.py - 解决打包后的导入问题
import sys
import os

# 添加当前目录到Python路径
if hasattr(sys, '_MEIPASS'):
    # PyInstaller打包后的临时目录
    base_path = sys._MEIPASS
else:
    # 开发环境
    base_path = os.path.dirname(os.path.abspath(__file__))

sys.path.insert(0, base_path)

# 导入所有必要的模块
try:
    from flask import Flask, render_template, request, jsonify, send_file
    import json
    import requests
    import os
    from datetime import datetime, timedelta
    from urllib.parse import quote, quote_plus, urlparse, parse_qs
    import io
    import threading
    import queue
    import time
    from PIL import Image
    import tempfile
    import zipfile
    import pyperclip
    import webbrowser
    import tkinter as tk
    from tkinter import messagebox
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography.hazmat.primitives import hashes, padding
    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
    import configparser
    import re
    from bs4 import BeautifulSoup
except ImportError as e:
    print(f"导入模块失败: {e}")
    input("按回车键退出...")
    sys.exit(1)

# 设置模板路径
if hasattr(sys, '_MEIPASS'):
    # PyInstaller打包后的模板路径
    template_folder = os.path.join(sys._MEIPASS, 'templates')
    app = Flask(__name__, template_folder=template_folder)
else:
    # 开发环境
    app = Flask(__name__)

class ConfigManager:
    """配置管理系统"""
    def __init__(self):
        self.config_file = "settings.ini"
        self.config = configparser.ConfigParser(interpolation=None)
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        print(f"🔧 加载配置文件: {self.config_file}")
        if os.path.exists(self.config_file):
            self.config.read(self.config_file, encoding='utf-8')
            print(f"✅ 配置文件加载成功")
            # 显示配置概要
            for section in self.config.sections():
                print(f"  [{section}] 包含 {len(self.config[section])} 个配置项")
        else:
            print(f"⚠️ 配置文件不存在，创建默认配置")
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置文件"""
        self.config['API'] = {
            'url': 'https://www.dianxiaomi.com/package/list.htm?pageNo=1&pageSize=100&shopId=-1&state=paid&platform=&isSearch=0&searchType=orderId&authId=-1&startTime=&endTime=&country=&orderField=order_pay_time&isVoided=0&isRemoved=0&ruleId=-1&sysRule=&applyType=&applyStatus=&printJh=-1&printMd=-1&commitPlatform=&productStatus=&jhComment=-1&storageId=0&isOversea=-1&isFree=0&isBatch=0&history=&custom=-1&timeOut=0&refundStatus=0&buyerAccount=&forbiddenStatus=-1&forbiddenReason=0&behindTrack=-1&orderId=',
            'cookie': '',
            'base_url': 'https://www.dianxiaomi.com',
            'sku_search_url': 'https://www.dianxiaomi.com/api/popTemuProduct/pageList.json',
            'referer': 'https://www.dianxiaomi.com/'
        }
        self.config['SHARED'] = {
            'folder': r'\\192.168.1.200\hhr-图库\合伙人-半托出单图\亚克力摆件\丽生-亚克力摆件'
        }
        self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
    
    def get_api_url(self):
        """获取API地址"""
        return self.config.get('API', 'url', fallback='')
    
    def get_cookie(self):
        """获取Cookie"""
        return self.config.get('API', 'cookie', fallback='')
    
    def get_base_url(self):
        """获取基础URL"""
        return self.config.get('API', 'base_url', fallback='https://www.dianxiaomi.com')
    
    def get_sku_search_url(self):
        """获取SKU搜索URL"""
        return self.config.get('API', 'sku_search_url', 
                              fallback='https://www.dianxiaomi.com/api/popTemuProduct/pageList.json')
    
    def get_referer(self):
        """获取Referer"""
        return self.config.get('API', 'referer', fallback='https://www.dianxiaomi.com/')
    
    def get_shared_folder(self):
        """获取共享文件夹路径"""
        return self.config.get('SHARED', 'folder', fallback='')
    
    def update_config(self, section, key, value):
        """更新配置"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value
        self.save_config()

class OrderHTMLParser:
    """HTML数据解析器"""
    def __init__(self):
        pass
    
    def parse_order_html(self, html_content):
        """解析订单HTML数据"""
        try:
            print("🔍 开始解析HTML内容...")
            print(f"HTML内容长度: {len(html_content)}")
            
            soup = BeautifulSoup(html_content, 'html.parser')
            orders = []
            
            # 查找所有订单行
            print("🔍 查找订单行...")
            order_rows = soup.find_all('tr', class_=lambda x: x and 'orderId_' in x)
            print(f"找到 {len(order_rows)} 个订单行")
            
            if not order_rows:
                print("❌ 未找到订单行，可能是HTML结构发生变化")
                # 尝试查找其他可能的订单标识
                all_trs = soup.find_all('tr')
                print(f"总共找到 {len(all_trs)} 个<tr>标签")
                
                # 查找包含class的tr
                trs_with_class = [tr for tr in all_trs if tr.get('class')]
                print(f"包含class的<tr>标签: {len(trs_with_class)}")
                for i, tr in enumerate(trs_with_class[:5]):  # 只显示前5个
                    print(f"  TR {i+1} class: {tr.get('class')}")
                
                return []
            
            for i, row in enumerate(order_rows):
                try:
                    print(f"📦 解析第 {i+1}/{len(order_rows)} 个订单行...")
                    
                    # 提取订单ID
                    order_id_class = [cls for cls in row.get('class', []) if 'orderId_' in cls]
                    order_id = order_id_class[0].replace('orderId_', '') if order_id_class else ''
                    print(f"  订单ID: {order_id}")
                    
                    # 提取SKU
                    sku_link = row.find('a', class_='pairProInfoSku')
                    sku = sku_link.text.strip() if sku_link else ''
                    print(f"  SKU: {sku}")
                    
                    if not sku:
                        print("  ⚠️ SKU为空，可能是HTML结构变化")
                        # 尝试其他方式查找SKU
                        all_links = row.find_all('a')
                        print(f"  该行总共有 {len(all_links)} 个链接")
                        for j, link in enumerate(all_links):
                            print(f"    链接 {j+1}: class={link.get('class')}, text={link.text.strip()[:20]}")
                    
                    # 提取图片URL
                    img_element = row.find('img', class_='pairProInfoImg')
                    image_url = ''
                    if img_element:
                        # 优先从data-original属性获取
                        image_url = img_element.get('data-original', '')
                        # 如果为空，从src属性获取
                        if not image_url:
                            image_url = img_element.get('src', '')
                        print(f"  图片URL: {image_url[:50]}...")
                    else:
                        print("  ⚠️ 未找到图片元素")
                        # 尝试查找其他图片
                        all_imgs = row.find_all('img')
                        print(f"  该行总共有 {len(all_imgs)} 个图片")
                        for j, img in enumerate(all_imgs):
                            print(f"    图片 {j+1}: class={img.get('class')}, src={img.get('src', '')[:30]}...")
                    
                    # 提取数量
                    quantity_span = row.find('span', class_=lambda x: x and ('circularSpan' in x))
                    quantity = quantity_span.text.strip() if quantity_span else '1'
                    print(f"  数量: {quantity}")
                    
                    # 提取订单号
                    order_number_span = row.find('input', class_=lambda x: x and 'orderNumberSpan_' in x)
                    order_number = order_number_span.get('value', '') if order_number_span else ''
                    print(f"  订单号: {order_number}")
                    
                    # 提取价格信息
                    price = '0'
                    price_info = row.find('td', class_='minW100')
                    if price_info:
                        price_text = price_info.get_text()
                        price_match = re.search(r'CNY\s*(\d+(?:\.\d+)?)', price_text)
                        price = price_match.group(1) if price_match else '0'
                    print(f"  价格: {price}")
                    
                    if sku:  # 只有当SKU存在时才添加订单
                        order_data = {
                            'order_id': order_id,
                            'sku': sku,
                            'image_url': image_url,
                            'quantity': quantity,
                            'order_number': order_number,
                            'price': price,
                            'product_name': ''  # 后续通过API获取
                        }
                        orders.append(order_data)
                        print(f"  ✅ 订单解析成功")
                    else:
                        print(f"  ❌ SKU为空，跳过该订单")
                        
                except Exception as e:
                    print(f"  ❌ 解析订单行失败: {str(e)}")
                    import traceback
                    print(f"  详细错误: {traceback.format_exc()}")
                    continue
            
            print(f"✅ HTML解析完成，成功解析 {len(orders)} 个订单")
            return orders
            
        except Exception as e:
            print(f"❌ 解析HTML失败: {str(e)}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")
            return []

class ImageDownloader:
    """图片下载功能模块"""
    target_path_suffix = r"\导出图\已完成"

    @staticmethod
    def format_size(bytes_size):
        """格式化文件大小"""
        if bytes_size == 0:
            return "0 MB"
        mb_size = bytes_size / (1024 * 1024)
        if mb_size < 1024:
            return f"{mb_size:.2f} MB"
        return f"{mb_size / 1024:.2f} GB"

    @classmethod
    def should_download_file(cls, file_path, file_name):
        """检查文件是否符合下载条件"""
        if cls.target_path_suffix:
            lower_file_path = file_path.lower()
            lower_suffix = cls.target_path_suffix.lower()
            
            if not lower_file_path.endswith(lower_suffix + '\\' + file_name.lower()) and \
               not lower_file_path.endswith(lower_suffix + '/' + file_name.lower()):
                return False
                
        return os.path.splitext(file_name)[1].lower() in ('.png', '.jpg', '.jpeg')

    @classmethod
    def prepare_directory(cls, base_dir):
        """准备下载目录"""
        os.makedirs(base_dir, exist_ok=True)
        return base_dir

class ImageProcessor:
    def __init__(self):
        self.search_base_path = r"E:\图片\原图"
        self.current_output_dir = ""
        self.log_messages = []
        self.config_manager = ConfigManager()
        self.html_parser = OrderHTMLParser()

    def log(self, message, message_type='info'):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_messages.append({
            'time': timestamp,
            'message': message,
            'type': message_type
        })
        print(f"[{timestamp}] {message}")

    def get_fulfilment_id(self, order):
        """获取fulfilmentProductSkuId"""
        try:
            sku_list = order.get('skuQuantityDetailList', [])
            if not sku_list:
                self.log("⚠️ skuQuantityDetailList为空", 'warning')
                return "无FulfilmentID"
            return str(sku_list[0].get('fulfilmentProductSkuId', '无FulfilmentID'))
        except Exception as e:
            self.log(f"获取fulfilmentID异常: {str(e)}", 'error')
            return "错误ID"

    def download_image(self, url, save_path):
        """下载图片文件"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.example.com/'
        }

        try:
            response = requests.get(url, headers=headers, stream=True, timeout=15)
            response.raise_for_status()

            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(1024):
                        f.write(chunk)
                return True
            return False
        except Exception as e:
            self.log(f"下载异常: {str(e)}", 'error')
            return False

    def detect_image_extension(self, url):
        """根据URL猜测图片扩展名"""
        if 'jpeg' in url.lower() or 'jpg' in url.lower():
            return '.jpg'
        elif 'png' in url.lower():
            return '.png'
        elif 'gif' in url.lower():
            return '.gif'
        elif 'webp' in url.lower():
            return '.webp'
        return '.jpg'

    def download_api_image(self, url, save_path, max_retries=3, retry_interval=2):
        """下载API图片，带重试机制"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.example.com/',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8'
        }
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    self.log(f"🔄 第 {attempt + 1} 次重试下载API图片...", 'info')
                    time.sleep(retry_interval)
                
                response = requests.get(url, headers=headers, stream=True, timeout=15)
                response.raise_for_status()
                
                if response.status_code == 200:
                    with open(save_path, 'wb') as f:
                        for chunk in response.iter_content(1024):
                            f.write(chunk)
                    self.log(f"✅ API图片下载成功", 'success')
                    return True
            except Exception as e:
                self.log(f"❌ 第 {attempt + 1} 次下载API图片失败: {str(e)}", 'error')
        
        return False

    def search_local_images(self, product_name, search_path="", strict_search=True):
        """搜索本地图片"""
        cleaned_name = product_name.replace("2D Flat ", "").strip()
        
        # 首先尝试使用EverythingAPI
        try:
            self.log(f"尝试使用EverythingAPI搜索图片: {cleaned_name}", 'info')
            search_query = cleaned_name
            
            if search_path and strict_search:
                if not search_path.endswith('\\'):
                    search_path += '\\'
                search_query = f"path:\"{search_path}\" {search_query}"
                    
            search_params = {
                "search": search_query,
                "json": 1,
                "path_column": 1,
                "size_column": 1,
                "sort": "name",
                "ascending": 1
            }
            
            search_url = "http://localhost:8080/"
            response = requests.get(search_url, params=search_params, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            valid_files = []
            for item in data.get("results", []):
                file_name = item.get('name', '')
                file_path = f"{item.get('path', '')}\\{file_name}".replace("\\\\", "\\")
                
                if ImageDownloader.should_download_file(file_path, file_name):
                    valid_files.append({
                        'name': file_name,
                        'path': file_path,
                        'size': item.get('size', 0),
                        'url': f"http://127.0.0.1:8080/{quote(file_path)}"
                    })
            
            self.log(f"✅ EverythingAPI搜索成功，找到 {len(valid_files)} 张图片", 'success')
            return valid_files
            
        except Exception as e:
            self.log(f"⚠️ EverythingAPI不可用: {str(e)}", 'warning')
            self.log(f"尝试使用文件系统搜索...", 'info')
            
            # 回退到文件系统搜索
            return self.search_local_images_fallback(cleaned_name, search_path, strict_search)

    def check_sku_exists_in_shared_folder(self, sku):
        """检查SKU对应的图片是否在共享文件夹中存在"""
        try:
            shared_folder = self.config_manager.get_shared_folder()
            if not shared_folder:
                self.log("⚠️ 未配置共享文件夹路径", 'warning')
                return False
            
            if not os.path.exists(shared_folder):
                self.log(f"⚠️ 共享文件夹不存在或无法访问: {shared_folder}", 'warning')
                return False
            
            # 检查常见的图片格式
            image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
            
            for ext in image_extensions:
                image_path = os.path.join(shared_folder, f"{sku}{ext}")
                if os.path.exists(image_path):
                    self.log(f"✅ 找到共享文件夹中的SKU图片: {sku}{ext}", 'info')
                    return True
            
            self.log(f"❌ 共享文件夹中未找到SKU图片: {sku}", 'info')
            return False
            
        except Exception as e:
            self.log(f"检查共享文件夹SKU图片异常: {str(e)}", 'error')
            return False

    def filter_orders_by_shared_folder(self, orders):
        """根据共享文件夹中的SKU图片筛选订单，只返回共享文件夹中不存在的SKU"""
        try:
            shared_folder = self.config_manager.get_shared_folder()
            if not shared_folder:
                self.log("⚠️ 未配置共享文件夹路径，跳过筛选", 'warning')
                return orders
            
            self.log(f"🔍 开始检查共享文件夹: {shared_folder}", 'info')
            
            if not os.path.exists(shared_folder):
                self.log(f"⚠️ 共享文件夹不存在或无法访问，跳过筛选: {shared_folder}", 'warning')
                return orders
            
            filtered_orders = []
            total_count = len(orders)
            existing_count = 0
            
            for order in orders:
                sku = order.get('sku', '')
                if not sku:
                    # SKU为空的订单直接保留
                    filtered_orders.append(order)
                    continue
                
                # 检查SKU图片是否在共享文件夹中存在
                if not self.check_sku_exists_in_shared_folder(sku):
                    # 不存在的SKU保留
                    filtered_orders.append(order)
                else:
                    # 存在的SKU跳过
                    existing_count += 1
            
            filtered_count = len(filtered_orders)
            self.log(f"📊 筛选结果: 总计 {total_count} 个SKU，共享文件夹已有 {existing_count} 个，需要处理 {filtered_count} 个", 'info')
            
            return filtered_orders
            
        except Exception as e:
            self.log(f"筛选订单异常: {str(e)}", 'error')
            return orders

    def search_local_images_fallback(self, product_name, search_path="", strict_search=True):
        """文件系统搜索图片（备用方案）"""
        try:
            valid_files = []
            
            # 如果没有指定搜索路径，使用默认路径
            if not search_path:
                search_path = self.search_base_path
            
            if not os.path.exists(search_path):
                self.log(f"搜索路径不存在: {search_path}", 'warning')
                return []
            
            self.log(f"在路径中搜索: {search_path}", 'info')
            
            # 获取商品名称的关键词
            keywords = [kw.strip().lower() for kw in product_name.split() if len(kw.strip()) > 1]
            if not keywords:
                keywords = [product_name.lower()]
            
            # 递归搜索文件
            for root, dirs, files in os.walk(search_path):
                # 如果启用严格搜索，检查路径限制
                if strict_search and ImageDownloader.target_path_suffix:
                    if not root.lower().endswith(ImageDownloader.target_path_suffix.lower()):
                        continue
                
                for file in files:
                    file_path = os.path.join(root, file)
                    file_lower = file.lower()
                    
                    # 检查文件扩展名
                    if not ImageDownloader.should_download_file(file_path, file):
                        continue
                    
                    # 检查文件名是否包含关键词
                    contains_keyword = any(keyword in file_lower for keyword in keywords)
                    if contains_keyword:
                        try:
                            file_size = os.path.getsize(file_path)
                            valid_files.append({
                                'name': file,
                                'path': file_path,
                                'size': file_size,
                                'url': f"file:///{quote(file_path.replace('\\', '/'))}"
                            })
                        except Exception as e:
                            self.log(f"获取文件信息失败: {file_path} - {str(e)}", 'warning')
                            continue
            
            self.log(f"✅ 文件系统搜索完成，找到 {len(valid_files)} 张图片", 'success')
            return valid_files
            
        except Exception as e:
            self.log(f"❌ 文件系统搜索失败: {str(e)}", 'error')
            return []

    def get_product_api_image(self, product_id, json_data):
        """获取商品的API图片URL"""
        try:
            raw_orders = json_data.get('result', {}).get('subOrderForSupplierList', [])
            
            for order in raw_orders:
                if not order.get('isFirst'):
                    continue
                    
                sku_list = order.get('skuQuantityDetailList', [])
                if not sku_list:
                    continue
                    
                sku_id = str(sku_list[0].get('fulfilmentProductSkuId', ''))
                if sku_id == product_id:
                    image_url = order.get('productSkcPicture', '')
                    if image_url:
                        return image_url
            
            return None
        except Exception as e:
            self.log(f"❌ 获取API图片URL失败: {str(e)}", 'error')
            return None

    def fetch_api_data(self):
        """从API获取数据"""
        try:
            url = self.config_manager.get_api_url()
            cookie = self.config_manager.get_cookie()
            
            # 详细的配置检查
            print(f"\n=== API调试信息 ===")
            print(f"API URL: {url}")
            print(f"Cookie长度: {len(cookie) if cookie else 0}")
            print(f"Cookie前50字符: {cookie[:50] if cookie else 'None'}...")
            
            if not url:
                error_msg = "❌ API地址未配置，请在配置管理中设置API地址"
                self.log(error_msg, 'error')
                print(f"错误: {error_msg}")
                return None
            
            if not cookie:
                error_msg = "❌ Cookie未配置，请在配置管理中设置Cookie"
                self.log(error_msg, 'error')
                print(f"错误: {error_msg}")
                return None
            
            self.log(f"🚀 开始从API获取数据...", 'info')
            print(f"🚀 正在请求: {url}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Cookie': cookie,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': self.config_manager.get_referer(),
                'X-Requested-With': 'XMLHttpRequest'
            }
            
            print("📋 请求头信息:")
            for key, value in headers.items():
                if key == 'Cookie':
                    print(f"  {key}: {value[:50]}...")
                else:
                    print(f"  {key}: {value}")
            
            response = requests.get(url, headers=headers, timeout=30)
            
            print(f"📡 响应状态码: {response.status_code}")
            print(f"📡 响应头: {dict(response.headers)}")
            print(f"📡 响应内容长度: {len(response.text)}")
            print(f"📡 响应内容前200字符: {response.text[:200]}")
            
            response.raise_for_status()
            
            # 检查Cookie是否过期
            if response.status_code == 200:
                response_text = response.text
                
                # 详细检查响应内容
                if 'login' in response_text.lower() or '登录' in response_text:
                    error_msg = "❌ Cookie可能已过期，请更新Cookie。响应内容包含登录相关信息"
                    self.log(error_msg, 'error')
                    print(f"错误: {error_msg}")
                    print(f"登录检测: 响应中包含 'login' 或 '登录' 关键字")
                    return None
                
                # 检查是否是真正的错误页面（更精确的判断）
                if ('error' in response_text.lower() and 'class="error"' in response_text.lower()) or \
                   ('错误' in response_text and '错误页面' in response_text):
                    error_msg = "❌ API返回错误页面"
                    self.log(error_msg, 'error')
                    print(f"错误: {error_msg}")
                    print(f"错误检测: 响应中包含真正的错误信息")
                    return None
                elif 'error' in response_text.lower() or '错误' in response_text:
                    print(f"⚠️ 响应中包含'error'或'错误'字样，但可能是正常内容，继续处理...")
                    # 保存响应内容用于调试
                    with open('debug_response.html', 'w', encoding='utf-8') as f:
                        f.write(response_text)
                    print("💾 已保存响应内容到 debug_response.html 文件用于检查")
                
                # 检查是否是HTML格式
                if response_text.strip().startswith('<'):
                    self.log("检测到HTML格式响应，开始解析", 'info')
                    print("✅ 检测到HTML格式响应")
                    
                    # 使用HTML解析器
                    print("🔍 开始解析HTML内容...")
                    orders = self.html_parser.parse_order_html(response_text)
                    print(f"🔍 HTML解析结果: 找到 {len(orders)} 条订单")
                    
                    if not orders:
                        error_msg = "❌ 未从HTML中解析到订单数据，可能是HTML结构发生变化"
                        self.log(error_msg, 'error')
                        print(f"错误: {error_msg}")
                        # 保存HTML用于调试
                        with open('debug_response.html', 'w', encoding='utf-8') as f:
                            f.write(response_text)
                        print("💾 已保存响应内容到 debug_response.html 文件，请检查HTML结构")
                        return None
                    
                    self.log(f"✅ 成功解析到 {len(orders)} 条订单", 'success')
                    print(f"✅ 成功解析订单，开始SKU筛选...")

                    # 先根据共享文件夹筛选订单，只保留不存在的SKU
                    print(f"🔍 开始根据共享文件夹筛选SKU...")
                    filtered_orders = self.filter_orders_by_shared_folder(orders)
                    print(f"📊 筛选完成: 原有 {len(orders)} 个SKU，筛选后 {len(filtered_orders)} 个SKU需要处理")

                    if not filtered_orders:
                        print("✅ 所有SKU在共享文件夹中都已存在，无需处理")
                        return {'orders': []}

                    # 只为筛选后的订单获取商品名称
                    print(f"🔍 开始为筛选后的 {len(filtered_orders)} 个SKU获取商品名称...")
                    success_count = 0
                    for i, order in enumerate(filtered_orders):
                        print(f"🔍 处理第 {i+1}/{len(filtered_orders)} 个缺失SKU: {order['sku']}")

                        # 直接通过SKU获取商品名称
                        if order['sku']:
                            print(f"  通过SKU搜索: {order['sku']}")
                            product_name = self.get_product_name_by_sku(order['sku'])

                            if product_name:
                                order['product_name'] = product_name
                                success_count += 1
                                print(f"  ✅ 找到商品名称: {product_name}")
                            else:
                                order['product_name'] = f"未找到商品名称 ({order['sku']})"
                                print(f"  ❌ 未找到商品名称")
                        else:
                            order['product_name'] = "SKU为空"
                            print(f"  ❌ SKU为空，跳过")

                    print(f"📊 缺失SKU商品名称获取统计: 成功 {success_count}/{len(filtered_orders)}")

                    return {'orders': filtered_orders}
                else:
                    # 尝试解析JSON格式
                    try:
                        json_data = response.json()
                        self.log("检测到JSON格式响应", 'info')
                        print("✅ 检测到JSON格式响应")
                        return json_data
                    except json.JSONDecodeError as e:
                        error_msg = f"❌ 响应格式无法识别，既不是HTML也不是JSON: {str(e)}"
                        self.log(error_msg, 'error')
                        print(f"错误: {error_msg}")
                        print(f"响应内容: {response_text[:500]}")
                        return None
            else:
                error_msg = f"❌ API请求失败，状态码: {response.status_code}"
                self.log(error_msg, 'error')
                print(f"错误: {error_msg}")
                return None
            
        except requests.exceptions.Timeout as e:
            error_msg = f"❌ 请求超时，请检查网络连接: {str(e)}"
            self.log(error_msg, 'error')
            print(f"错误: {error_msg}")
            return None
        except requests.exceptions.ConnectionError as e:
            error_msg = f"❌ 网络连接失败，请检查网络: {str(e)}"
            self.log(error_msg, 'error')
            print(f"错误: {error_msg}")
            return None
        except requests.exceptions.HTTPError as e:
            error_msg = f"❌ HTTP错误: {str(e)}"
            self.log(error_msg, 'error')
            print(f"错误: {error_msg}")
            return None
        except requests.exceptions.RequestException as e:
            error_msg = f"❌ 网络请求失败: {str(e)}"
            self.log(error_msg, 'error')
            print(f"错误: {error_msg}")
            return None
        except Exception as e:
            error_msg = f"❌ 获取数据失败: {str(e)}"
            self.log(error_msg, 'error')
            print(f"错误: {error_msg}")
            import traceback
            print(f"详细错误信息: {traceback.format_exc()}")
            return None

    def get_product_name_by_sku(self, sku):
        """通过SKU获取商品名称"""
        try:
            if not sku:
                print(f"    ⚠️ SKU为空，跳过")
                return None
            
            print(f"    🔍 通过SKU搜索商品: {sku}")
            url = self.config_manager.get_sku_search_url()
            cookie = self.config_manager.get_cookie()
            
            print(f"    📡 SKU搜索URL: {url}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Cookie': cookie,
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': self.config_manager.get_referer(),
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
            }
            
            search_data = {
                'sortName': '2',
                'pageNo': '1',
                'pageSize': '50',
                'searchType': '2',
                'searchValue': sku,
                'productSearchType': '1',
                'shopId': '-1',
                'dxmState': 'online',
                'site': '0',
                'fullCid': '',
                'sortValue': '2',
                'productType': '',
                'productStatus': 'ONLINE'
            }
            
            print(f"    📋 搜索参数: {search_data}")
            
            response = requests.post(url, headers=headers, data=search_data, timeout=15)
            print(f"    📡 SKU搜索响应状态码: {response.status_code}")
            print(f"    📡 SKU搜索响应长度: {len(response.text)}")
            
            response.raise_for_status()
            
            try:
                json_data = response.json()
                print(f"    ✅ 成功解析JSON响应")
            except json.JSONDecodeError as e:
                print(f"    ❌ JSON解析失败: {e}")
                print(f"    响应内容: {response.text[:200]}")
                return None
            
            products = json_data.get('data', {}).get('page', {}).get('list', [])
            print(f"    🔍 找到 {len(products)} 个商品")
            
            if not products:
                print(f"    ❌ SKU搜索无结果")
                return None
            
            # SKU搜索返回的就是匹配的商品，直接使用第一个
            if len(products) >= 1:
                product = products[0]
                product_name = product.get('productName', '')
                product_id = str(product.get('productId', ''))
                print(f"    ✅ SKU搜索成功: ID={product_id}, 名称={product_name}")
                return product_name
            
            print(f"    ❌ SKU搜索无结果")
            return None
            
        except requests.exceptions.RequestException as e:
            error_msg = f"SKU搜索网络请求失败: {str(e)}"
            print(f"    ❌ {error_msg}")
            self.log(error_msg, 'error')
            return None
        except Exception as e:
            error_msg = f"通过SKU获取商品名称失败: {str(e)}"
            print(f"    ❌ {error_msg}")
            self.log(error_msg, 'error')
            import traceback
            print(f"    详细错误: {traceback.format_exc()}")
            return None

    def get_product_name_by_image_url(self, image_url):
        """通过图片URL获取商品名称"""
        try:
            if not image_url:
                print(f"    ⚠️ 图片URL为空，跳过")
                return None
            
            print(f"    🔍 通过图片URL搜索商品: {image_url[:50]}...")
            
            # 从图片URL中提取32位十六进制ID
            img_id_match = re.search(r'/([a-f0-9]{32})-', image_url)
            if not img_id_match:
                print(f"    ❌ 无法从图片URL中提取图片ID")
                return None
            
            img_id = img_id_match.group(1)
            print(f"    🔍 提取到图片ID: {img_id}")
            
            url = self.config_manager.get_sku_search_url()
            cookie = self.config_manager.get_cookie()
            
            print(f"    📡 图片搜索URL: {url}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Cookie': cookie,
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Referer': self.config_manager.get_referer(),
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
            }
            
            # 获取所有商品进行匹配
            search_data = {
                'sortName': '2',
                'pageNo': '1',
                'pageSize': '100',  # 大页面获取更多商品
                'searchType': '2',
                'searchValue': '',  # 空值获取所有商品
                'productSearchType': '1',
                'shopId': '-1',
                'dxmState': 'online',
                'site': '0',
                'fullCid': '',
                'sortValue': '2',
                'productType': '',
                'productStatus': 'ONLINE'
            }
            
            print(f"    📋 图片搜索参数: {search_data}")
            
            response = requests.post(url, headers=headers, data=search_data, timeout=15)
            print(f"    📡 图片搜索响应状态码: {response.status_code}")
            print(f"    📡 图片搜索响应长度: {len(response.text)}")
            
            response.raise_for_status()
            
            try:
                json_data = response.json()
                print(f"    ✅ 成功解析图片搜索JSON响应")
            except json.JSONDecodeError as e:
                print(f"    ❌ 图片搜索JSON解析失败: {e}")
                print(f"    响应内容: {response.text[:200]}")
                return None
            
            products = json_data.get('data', {}).get('page', {}).get('list', [])
            print(f"    🔍 图片搜索找到 {len(products)} 个商品")
            
            if not products:
                print(f"    ❌ 图片搜索无结果")
                return None
            
            # 通过图片ID匹配商品
            for i, product in enumerate(products):
                material_img_url = product.get('materialImgUrl', '')
                product_name = product.get('productName', '')
                
                print(f"    📦 商品 {i+1}: 名称={product_name[:30]}..., 图片URL={material_img_url[:50]}...")
                
                # 检查商品主图是否包含相同的图片ID
                if material_img_url and img_id in material_img_url:
                    print(f"    ✅ 通过图片ID匹配成功: {product_name}")
                    return product_name
            
            print(f"    ❌ 图片ID匹配失败")
            return None
            
        except requests.exceptions.RequestException as e:
            error_msg = f"图片URL搜索网络请求失败: {str(e)}"
            print(f"    ❌ {error_msg}")
            self.log(error_msg, 'error')
            return None
        except Exception as e:
            error_msg = f"通过图片URL获取商品名称失败: {str(e)}"
            print(f"    ❌ {error_msg}")
            self.log(error_msg, 'error')
            import traceback
            print(f"    详细错误: {traceback.format_exc()}")
            return None

    def download_product_image(self, sku, image_url):
        """下载商品图片到本地"""
        try:
            if not image_url or not sku or sku in ["无FulfilmentID", "错误ID"]:
                self.log(f"跳过下载: SKU={sku}, 图片URL为空或无效", 'warning')
                return None
            
            if not self.current_output_dir:
                self.log("输出目录未设置，跳过图片下载", 'warning')
                return None
            
            # 创建商品图目录
            product_images_dir = os.path.join(self.current_output_dir, "商品图")
            os.makedirs(product_images_dir, exist_ok=True)
            
            # 检测文件扩展名
            ext = os.path.splitext(image_url)[1].split('?')[0]
            if not ext:
                ext = self.detect_image_extension(image_url)
            
            # 生成文件路径
            filename = f"{sku}{ext}"
            local_path = os.path.join(product_images_dir, filename)
            
            # 检查文件是否已存在
            if os.path.exists(local_path):
                self.log(f"商品图已存在: {filename}", 'info')
                return local_path
            
            # 下载图片
            self.log(f"正在下载商品图: {filename}", 'info')
            if self.download_api_image(image_url, local_path):
                self.log(f"✅ 商品图下载成功: {filename}", 'success')
                return local_path
            else:
                self.log(f"❌ 商品图下载失败: {filename}", 'error')
                return None
                
        except Exception as e:
            self.log(f"下载商品图异常: {str(e)}", 'error')
            return None

    def get_orders_summary_without_download(self, data):
        """获取订单摘要，不下载商品图片"""
        try:
            summary_orders = []

            # 处理HTML格式数据
            if 'orders' in data:
                orders = data.get('orders', [])
                for order in orders:
                    summary_orders.append({
                        'id': order.get('sku', ''),
                        'name': order.get('product_name', ''),
                        'sku': order.get('sku', ''),
                        'order_id': order.get('order_id', ''),
                        'order_number': order.get('order_number', ''),
                        'quantity': order.get('quantity', '1'),
                        'price': order.get('price', '0'),
                        'image_url': order.get('image_url', ''),
                        'local_image_path': None
                    })
            else:
                # 处理原有JSON格式数据（兼容）
                raw_orders = data.get('result', {}).get('subOrderForSupplierList', [])
                for order in raw_orders:
                    if not order.get('isFirst'):
                        continue

                    sku_list = order.get('skuQuantityDetailList', [])
                    if sku_list:
                        sku_info = sku_list[0]
                        sku_id = str(sku_info.get('fulfilmentProductSkuId', ''))
                        image_url = order.get('productSkcPicture', '')

                        summary_orders.append({
                            'id': sku_id,
                            'name': order.get('productSkuName', ''),
                            'sku': sku_id,
                            'order_id': order.get('orderId', ''),
                            'order_number': order.get('orderNumber', ''),
                            'quantity': str(sku_info.get('quantity', 1)),
                            'price': str(order.get('orderItemPrice', 0)),
                            'image_url': image_url,
                            'local_image_path': None
                        })

            return summary_orders

        except Exception as e:
            self.log(f"生成订单摘要失败: {str(e)}", 'error')
            return []

    def download_images_for_orders(self, orders):
        """为筛选后的订单下载商品图片"""
        try:
            for order in orders:
                sku = order.get('sku', '')
                image_url = order.get('image_url', '')

                # 下载商品图片
                local_image_path = self.download_product_image(sku, image_url)
                order['local_image_path'] = local_image_path

            return orders

        except Exception as e:
            self.log(f"下载商品图片失败: {str(e)}", 'error')
            return orders



def validate_key():
    """验证 key.vdf 文件的有效性"""
    # 获取正确的key.vdf路径
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller打包后，key.vdf应该与exe在同一目录
        exe_dir = os.path.dirname(sys.executable)
        key_file = os.path.join(exe_dir, "key.vdf")
    else:
        # 开发环境
        key_file = "key.vdf"
    
    if not os.path.exists(key_file):
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("错误", f"未找到授权文件 {key_file}")
            root.destroy()
        except:
            print(f"错误: 未找到授权文件 {key_file}")
        sys.exit(1)

    # 密钥生成配置（必须与加密时一致）
    password = os.getenv('KEY_PASSWORD', b'my_super_secret_password')
    salt = os.getenv('KEY_SALT', b'fixed_salt_value')

    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = kdf.derive(password)

    # 读取加密文件
    try:
        with open(key_file, "rb") as f:
            data = f.read()
            iv = data[:16]
            ciphertext = data[16:]

        # 解密数据
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
        decryptor = cipher.decryptor()
        padded_data = decryptor.update(ciphertext) + decryptor.finalize()

        # 移除填充
        unpadder = padding.PKCS7(128).unpadder()
        current_time_bytes = unpadder.update(padded_data) + unpadder.finalize()

        # 验证时间有效性
        stored_time = datetime.fromisoformat(current_time_bytes.decode('utf-8'))

        # 有效期验证（30天）
        if datetime.now() - stored_time > timedelta(days=30):
            try:
                root = tk.Tk()
                root.withdraw()
                messagebox.showerror("错误", "软件授权已过期")
                root.destroy()
            except:
                print("错误: 软件授权已过期")
            sys.exit(1)
            
    except Exception as e:
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("错误", f"授权验证失败: {str(e)}")
            root.destroy()
        except:
            print(f"错误: 授权验证失败: {str(e)}")
        sys.exit(1)

# 全局处理器实例
processor = ImageProcessor()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/fetch_data', methods=['POST'])
def fetch_data():
    """从API获取数据"""
    try:
        # 清空之前的日志
        processor.log_messages = []
        
        # 创建输出目录（在获取数据前就创建）
        now = datetime.now()
        # 格式化时间为中文格式：2025年12月3日下午3点42分
        year = now.year
        month = now.month
        day = now.day
        hour = now.hour
        minute = now.minute

        # 判断上午/下午
        if hour < 12:
            period = "上午"
            display_hour = hour if hour > 0 else 12
        else:
            period = "下午"
            display_hour = hour if hour <= 12 else hour - 12

        timestamp_chinese = f"{year}年{month}月{day}日{period}{display_hour}点{minute}分"
        folder_name = f"缺失SKU下载_{timestamp_chinese}_共X个"  # 临时名称，后面会更新
        os.makedirs(folder_name, exist_ok=True)
        processor.current_output_dir = folder_name
        processor.log(f"📁 创建输出目录: {folder_name}", 'info')
        
        # 从API获取数据
        data = processor.fetch_api_data()
        
        if data is None:
            return jsonify({
                'success': False,
                'message': '获取数据失败，请检查配置和网络连接',
                'logs': processor.log_messages
            })
        
        # 先获取订单摘要（不下载图片）
        orders = processor.get_orders_summary_without_download(data)

        # 进行SKU筛选，只保留共享文件夹中不存在的SKU
        processor.log("🔍 开始SKU筛选，只处理共享文件夹中不存在的SKU...", 'info')
        original_count = len(orders)
        filtered_orders = processor.filter_orders_by_shared_folder(orders)
        processor.log(f"📊 SKU筛选完成: 原有 {original_count} 个SKU，筛选后 {len(filtered_orders)} 个SKU需要处理", 'info')

        # 只为筛选后的订单下载商品图片
        processor.log("📥 开始下载筛选后的商品图片...", 'info')
        orders = processor.download_images_for_orders(filtered_orders)
        
        if not orders:
            return jsonify({
                'success': False,
                'message': '未找到有效的订单数据',
                'logs': processor.log_messages
            })
        
        # 更新目录名称为正确的商品数量
        correct_folder_name = f"缺失SKU下载_{timestamp_chinese}_共{len(orders)}个"
        if folder_name != correct_folder_name:
            import shutil
            if os.path.exists(correct_folder_name):
                shutil.rmtree(correct_folder_name)
            os.rename(folder_name, correct_folder_name)
            processor.current_output_dir = correct_folder_name
            processor.log(f"📁 目录重命名为: {correct_folder_name}", 'info')
        
        # 保存商品列表
        txt_path = os.path.join(processor.current_output_dir, "商品列表.txt")
        with open(txt_path, 'w', encoding='utf-8') as f:
            for order in orders:
                f.write(f"{order['name']}----{order['id']}\n")
        
        processor.log(f"✅ 数据处理完成，商品图片已下载到: {processor.current_output_dir}/商品图/", 'success')
        
        # 构建成功消息，包含筛选统计信息
        shared_folder = processor.config_manager.get_shared_folder()
        if shared_folder and os.path.exists(shared_folder):
            message = f'成功获取数据并完成筛选，需要处理 {len(orders)} 个SKU（已跳过共享文件夹中已有的SKU），商品图片已自动下载'
        else:
            message = f'成功获取 {len(orders)} 条订单数据，商品图片已自动下载（未配置或无法访问共享文件夹，跳过SKU筛选）'
        
        return jsonify({
            'success': True,
            'message': message,
            'products': orders,
            'output_dir': processor.current_output_dir,
            'logs': processor.log_messages
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'系统异常: {str(e)}'})

@app.route('/api/get_config', methods=['GET'])
def get_config():
    """获取配置信息"""
    try:
        config = {
            'api_url': processor.config_manager.get_api_url(),
            'cookie': processor.config_manager.get_cookie()[:50] + '...' if len(processor.config_manager.get_cookie()) > 50 else processor.config_manager.get_cookie(),
            'base_url': processor.config_manager.get_base_url(),
            'sku_search_url': processor.config_manager.get_sku_search_url(),
            'referer': processor.config_manager.get_referer(),
            'shared_folder': processor.config_manager.get_shared_folder()
        }
        
        return jsonify({
            'success': True,
            'config': config
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'获取配置失败: {str(e)}'})

@app.route('/api/update_config', methods=['POST'])
def update_config():
    """更新配置信息"""
    try:
        data = request.get_json()
        
        # 更新配置
        if 'api_url' in data:
            processor.config_manager.update_config('API', 'url', data['api_url'])
        
        if 'cookie' in data:
            processor.config_manager.update_config('API', 'cookie', data['cookie'])
        
        if 'base_url' in data:
            processor.config_manager.update_config('API', 'base_url', data['base_url'])
        
        if 'sku_search_url' in data:
            processor.config_manager.update_config('API', 'sku_search_url', data['sku_search_url'])
        
        if 'referer' in data:
            processor.config_manager.update_config('API', 'referer', data['referer'])
        
        if 'shared_folder' in data:
            processor.config_manager.update_config('SHARED', 'folder', data['shared_folder'])
        
        return jsonify({
            'success': True,
            'message': '配置更新成功'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'更新配置失败: {str(e)}'})



@app.route('/api/search_images', methods=['POST'])
def search_images():
    """搜索本地图片，并显示商品图"""
    try:
        data = request.get_json()
        product_name = data.get('product_name', '')
        product_id = data.get('product_id', '')
        search_path = data.get('search_path', processor.search_base_path)
        strict_search = data.get('strict_search', True)
        json_data = data.get('json_data', {})
        
        if not product_name:
            return jsonify({'success': False, 'message': '请提供商品名称'})
        
        # 首先检查是否有已下载的商品图
        product_image_path = None
        api_image_url_proxied = None
        api_image_url_raw = None
        
        if processor.current_output_dir and product_id:
            # 查找已下载的商品图
            product_images_dir = os.path.join(processor.current_output_dir, "商品图")
            if os.path.exists(product_images_dir):
                for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']:
                    potential_path = os.path.join(product_images_dir, f"{product_id}{ext}")
                    if os.path.exists(potential_path):
                        product_image_path = potential_path
                        processor.log(f"找到已下载的商品图: {product_image_path}", 'info')
                        break
        
        # 如果没有找到已下载的商品图，尝试从API获取
        if not product_image_path and json_data and product_id:
            api_image_url_raw = processor.get_product_api_image(product_id, json_data)
            if api_image_url_raw:
                api_image_url_proxied = f"/api/image_proxy?url={quote_plus(api_image_url_raw)}"
        
        # 构建商品图URL（优先使用本地已下载的）
        if product_image_path:
            api_image_url_proxied = f"/api/local_image?path={quote_plus(product_image_path)}"
            processor.log(f"使用本地商品图: {product_image_path}", 'info')
        
        # 搜索本地图片
        local_images = processor.search_local_images(product_name, search_path, strict_search)
        
        return jsonify({
            'success': True,
            'local_images': local_images,
            'api_image_url': api_image_url_proxied,
            'api_image_url_raw': api_image_url_raw,
            'product_image_path': product_image_path,
            'total_count': len(local_images)
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'搜索失败: {str(e)}'})

@app.route('/api/download_selected', methods=['POST'])
def download_selected():
    """下载选中的图片"""
    try:
        data = request.get_json()
        product_id = data.get('product_id', '')
        # 前端现在发送 'type', 'url', 和 'extension'
        image_url = data.get('url', '')
        file_extension = data.get('extension', '.jpg')
        
        if not product_id or not image_url:
            return jsonify({'success': False, 'message': '缺少商品ID或图片URL'})
        
        if not processor.current_output_dir:
            return jsonify({'success': False, 'message': '请先解析JSON数据'})
        
        # 准备下载目录
        base_dir = os.path.join(processor.current_output_dir, "出单图下载")
        target_dir = ImageDownloader.prepare_directory(base_dir)
        new_name = f"{product_id}{file_extension}"
        local_path = os.path.join(target_dir, new_name)
        
        # 处理不同类型的图片URL
        if image_url.startswith('file:///'):
            # 本地文件，直接复制
            import shutil
            source_path = image_url.replace('file:///', '').replace('/', '\\')
            try:
                shutil.copy2(source_path, local_path)
                success = True
            except Exception as e:
                processor.log(f"复制本地文件失败: {str(e)}", 'error')
                success = False
        else:
            # 网络图片，下载
            success = processor.download_image(image_url, local_path)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'图片下载成功: {new_name}',
                'file_path': local_path
            })
        else:
            return jsonify({'success': False, 'message': '图片下载失败'})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'下载失败: {str(e)}'})


@app.route('/api/image_proxy')
def image_proxy():
    """图片代理服务，直接返回原图"""
    image_url = request.args.get('url')
    if not image_url:
        return "缺少图片URL参数", 400

    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(image_url, headers=headers, stream=True, timeout=15)
        response.raise_for_status()

        # 直接返回原图，不进行缩放处理
        img_data = response.content
        
        # 检测图片格式
        try:
            img = Image.open(io.BytesIO(img_data))
            img_format = img.format if img.format else 'JPEG'
            mimetype = f'image/{img_format.lower()}'
        except:
            # 如果无法检测格式，默认为JPEG
            mimetype = 'image/jpeg'
        
        return send_file(io.BytesIO(img_data), mimetype=mimetype)

    except Exception as e:
        print(f"图片代理错误: {e}")
        return "处理图片时出错", 500

@app.route('/api/local_image')
def local_image():
    """本地图片服务"""
    image_path = request.args.get('path')
    if not image_path:
        return "缺少图片路径参数", 400

    try:
        # 安全检查：确保路径存在且是图片文件
        if not os.path.exists(image_path):
            return "图片文件不存在", 404
        
        # 检查文件扩展名
        ext = os.path.splitext(image_path)[1].lower()
        if ext not in ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']:
            return "不支持的图片格式", 400
        
        # 读取并返回图片
        with open(image_path, 'rb') as f:
            img_data = f.read()
        
        # 检测图片格式
        try:
            img = Image.open(io.BytesIO(img_data))
            img_format = img.format if img.format else 'JPEG'
            mimetype = f'image/{img_format.lower()}'
        except:
            # 根据文件扩展名设置MIME类型
            if ext in ['.jpg', '.jpeg']:
                mimetype = 'image/jpeg'
            elif ext == '.png':
                mimetype = 'image/png'
            elif ext == '.gif':
                mimetype = 'image/gif'
            elif ext == '.webp':
                mimetype = 'image/webp'
            elif ext == '.bmp':
                mimetype = 'image/bmp'
            else:
                mimetype = 'image/jpeg'
        
        return send_file(io.BytesIO(img_data), mimetype=mimetype)

    except Exception as e:
        print(f"本地图片服务错误: {e}")
        return "处理图片时出错", 500


@app.route('/api/get_logs')
def get_logs():
    """获取日志信息"""
    return jsonify({'logs': processor.log_messages})



if __name__ == '__main__':
    try:
        # 验证授权文件
        validate_key()
        
        # 启动Flask应用
        port = 8724
        url = f'http://localhost:{port}'
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(1.5)  # 等待Flask服务启动
            webbrowser.open(url)
        
        # 在新线程中打开浏览器
        threading.Thread(target=open_browser, daemon=True).start()
        
        print(f"商品数据管理工具启动中...")
        print(f"服务地址: {url}")
        print(f"浏览器将自动打开，如未打开请手动访问上述地址")
        
        app.run(debug=False, host='0.0.0.0', port=port, use_reloader=False)
        
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        input("按回车键退出...")